# 岗位管理功能说明

## 概述
在机构中心的员工管理模块中新增了完整的岗位管理功能，包括创建岗位、删除岗位、岗位权限分配等核心功能。

## 功能特性

### 1. 岗位管理标签页
- **位置**: 员工管理页面 -> 岗位管理标签页
- **访问路径**: `/institution/staff` -> 点击"岗位管理"标签

### 2. 岗位创建功能
**功能描述**: 创建新的岗位并设置相关属性

**包含字段**:
- **基本信息**:
  - 岗位名称（必填）
  - 所属部门（行政部、教务部、财务部、市场部、人事部、IT部）
  - 岗位级别（初级、中级、高级、经理、总监）
  - 启用状态（启用/禁用）

- **岗位描述**（必填）:
  - 详细的岗位职责和工作内容描述

- **权限设置**:
  - 用户管理
  - 课程管理
  - 学员管理
  - 财务管理
  - 数据分析
  - 系统设置
  - 员工管理
  - 教师管理
  - 机构管理

- **任职要求**:
  - 动态添加/删除任职要求
  - 支持多条要求设置

### 3. 岗位列表展示
**功能描述**: 以卡片形式展示所有岗位信息

**显示内容**:
- 岗位名称和级别标签
- 所属部门
- 启用/禁用状态
- 岗位描述
- 权限列表（显示前3个，超出显示数量）
- 任职要求（显示前2条，超出显示数量）
- 当前使用该岗位的员工数量
- 操作按钮（编辑、删除）

### 4. 岗位编辑功能
**功能描述**: 修改现有岗位的所有属性

**特性**:
- 预填充现有岗位数据
- 支持修改所有岗位属性
- 实时更新岗位信息

### 5. 岗位删除功能
**功能描述**: 删除不需要的岗位

**安全机制**:
- 检查是否有员工正在使用该岗位
- 如果有员工使用，显示警告并阻止删除
- 确认对话框防止误删除

### 6. 搜索和筛选功能
**搜索功能**:
- 支持按岗位名称搜索
- 支持按岗位描述搜索
- 实时搜索，无需点击搜索按钮

**筛选功能**:
- 按部门筛选（所有部门、行政部、教务部等）
- 按状态筛选（所有状态、启用、禁用）
- 多条件组合筛选

### 7. 员工岗位关联
**功能描述**: 员工可以关联到具体岗位

**实现方式**:
- 员工数据结构中添加 `positionId` 字段
- 岗位列表显示当前使用该岗位的员工数量
- 删除岗位时检查关联的员工数量

## 数据结构

### 岗位数据结构 (Position)
```typescript
interface Position {
  id: string                    // 岗位ID
  name: string                  // 岗位名称
  description: string           // 岗位描述
  department: string            // 所属部门
  level: 'junior' | 'middle' | 'senior' | 'manager' | 'director'  // 岗位级别
  permissions: string[]         // 权限列表
  requirements: string[]        // 任职要求
  isActive: boolean            // 是否启用
  createdAt: string            // 创建时间
  updatedAt: string            // 更新时间
}
```

### 员工数据结构更新
```typescript
interface Staff {
  // ... 其他字段
  positionId?: string          // 关联的岗位ID（新增）
  // ... 其他字段
}
```

## 示例数据

### 预设岗位数据
1. **教务主管**
   - 部门: 教务部
   - 级别: 经理
   - 权限: 课程管理、学员管理、教师管理
   - 要求: 教育相关专业本科以上学历、3年以上教务管理经验、良好的沟通协调能力

2. **财务专员**
   - 部门: 财务部
   - 级别: 中级
   - 权限: 财务管理
   - 要求: 财务相关专业、熟练使用财务软件、具备会计从业资格证

3. **市场经理**
   - 部门: 市场部
   - 级别: 经理
   - 权限: 数据分析
   - 要求: 市场营销相关专业、2年以上市场工作经验、具备良好的客户沟通能力、熟悉数字营销工具

4. **IT专员**
   - 部门: IT部
   - 级别: 中级
   - 权限: 系统设置
   - 要求: 计算机相关专业、熟悉常用编程语言、具备系统运维经验
   - 状态: 禁用

## 用户界面设计

### 1. 标签页导航
- 员工列表标签页
- 岗位管理标签页
- 清晰的标签页切换效果

### 2. 岗位卡片设计
- 现代化卡片布局
- 清晰的信息层次
- 直观的状态指示
- 便捷的操作按钮

### 3. 表单设计
- 响应式表单布局
- 清晰的字段分组
- 动态的任职要求管理
- 友好的权限选择界面

### 4. 搜索筛选栏
- 直观的搜索输入框
- 下拉选择筛选器
- 实时筛选效果

## 技术实现

### 1. 状态管理
- 使用 React Hooks 管理组件状态
- 岗位列表状态 (`positions`)
- 表单显示状态 (`showPositionForm`)
- 编辑状态 (`editingPosition`)
- 搜索筛选状态

### 2. 表单处理
- 受控组件表单
- 实时数据验证
- 动态字段管理（任职要求）

### 3. 数据操作
- CRUD 操作完整实现
- 数据关联检查（删除前检查员工关联）
- 实时搜索筛选

### 4. 用户体验
- 确认对话框
- 加载状态提示
- 错误处理和提示
- 响应式设计

## 使用流程

### 创建岗位
1. 点击"岗位管理"标签页
2. 点击"+ 创建岗位"按钮
3. 填写岗位基本信息
4. 设置岗位权限
5. 添加任职要求
6. 点击"创建"按钮

### 编辑岗位
1. 在岗位列表中找到目标岗位
2. 点击"编辑"按钮
3. 修改岗位信息
4. 点击"更新"按钮

### 删除岗位
1. 在岗位列表中找到目标岗位
2. 点击"删除"按钮
3. 确认删除操作

### 搜索筛选岗位
1. 在搜索框中输入关键词
2. 选择部门筛选条件
3. 选择状态筛选条件
4. 查看筛选结果

## 后续扩展建议

1. **岗位模板**: 预设常用岗位模板，快速创建岗位
2. **岗位层级**: 支持岗位之间的上下级关系
3. **薪资范围**: 为岗位设置薪资范围参考
4. **技能要求**: 更详细的技能要求管理
5. **岗位统计**: 岗位使用情况统计分析
6. **批量操作**: 支持批量启用/禁用岗位
7. **岗位导入导出**: 支持Excel格式的岗位数据导入导出
8. **审批流程**: 岗位创建和修改的审批流程
