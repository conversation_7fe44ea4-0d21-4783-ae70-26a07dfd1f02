import { useState } from 'preact/hooks'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Position {
  id: string
  name: string
  description: string
  department: string
  level: 'junior' | 'middle' | 'senior' | 'manager' | 'director'
  permissions: string[]
  requirements: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface Staff {
  id: string
  name: string
  account: string
  email: string
  phone: string
  position: string
  positionId?: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  createdAt: string
  avatar?: string
  permissions: string[]
  salary?: number
  address?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
}

interface StaffFormData extends Omit<Staff, 'id'> {}
interface PositionFormData extends Omit<Position, 'id' | 'createdAt' | 'updatedAt'> {}

interface PermissionNode {
  id: string
  label: string
  children?: PermissionNode[]
}

// 权限树形结构组件
function PermissionTree({ permissions }: { permissions: string[] }) {
  const permissionTree: PermissionNode[] = [
    {
      id: 'system',
      label: '系统管理',
      children: [
        { id: 'system_settings', label: '系统设置' },
        { id: 'user_management', label: '用户管理' }
      ]
    },
    {
      id: 'education',
      label: '教务管理',
      children: [
        { id: 'course_management', label: '课程管理' },
        { id: 'student_management', label: '学员管理' },
        { id: 'teacher_management', label: '教师管理' }
      ]
    },
    {
      id: 'institution',
      label: '机构管理',
      children: [
        { id: 'staff_management', label: '员工管理' },
        { id: 'institution_management', label: '机构信息管理' }
      ]
    },
    {
      id: 'business',
      label: '业务管理',
      children: [
        { id: 'finance_management', label: '财务管理' },
        { id: 'data_analysis', label: '数据分析' }
      ]
    }
  ]

  const renderPermissionNode = (node: PermissionNode, level: number = 0) => {
    const hasPermission = permissions.includes(node.id)
    const hasChildPermissions = node.children?.some(child => permissions.includes(child.id))

    return (
      <div key={node.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <div className={`flex items-center py-1 ${level === 0 ? 'font-medium text-gray-700' : 'text-gray-600'}`}>
          {node.children ? (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <span>{node.label}</span>
              {hasChildPermissions && (
                <span className="ml-2 px-1 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
                  {node.children?.filter(child => permissions.includes(child.id)).length}
                </span>
              )}
            </div>
          ) : (
            <div className="flex items-center">
              {hasPermission ? (
                <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-4 h-4 mr-1 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clipRule="evenodd" />
                </svg>
              )}
              <span className={hasPermission ? 'text-green-700' : 'text-gray-500'}>{node.label}</span>
            </div>
          )}
        </div>
        {node.children && (
          <div className="ml-2">
            {node.children.map(child => renderPermissionNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {permissionTree.map(node => renderPermissionNode(node))}
    </div>
  )
}

// 岗位列表表格组件
function PositionTable({ positions, onEdit, onDelete }: {
  positions: Position[]
  onEdit: (position: Position) => void
  onDelete: (id: string) => void
}) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              岗位名称
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              岗位描述
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              创建时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              权限管理
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {positions.map((position) => (
            <tr key={position.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{position.name}</div>
                    <div className="text-sm text-gray-500">
                      {position.department === 'administration' ? '行政部' :
                       position.department === 'education' ? '教务部' :
                       position.department === 'finance' ? '财务部' :
                       position.department === 'marketing' ? '市场部' :
                       position.department === 'hr' ? '人事部' :
                       position.department === 'it' ? 'IT部' : position.department}
                      {' • '}
                      {position.level === 'junior' ? '初级' :
                       position.level === 'middle' ? '中级' :
                       position.level === 'senior' ? '高级' :
                       position.level === 'manager' ? '经理' :
                       position.level === 'director' ? '总监' : position.level}
                    </div>
                  </div>
                  <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${
                    position.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {position.isActive ? '启用' : '禁用'}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="text-sm text-gray-900 max-w-xs">
                  <p className="line-clamp-2">{position.description}</p>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {formatDate(position.createdAt)}
              </td>
              <td className="px-6 py-4">
                <div className="max-w-sm">
                  <PermissionTree permissions={position.permissions} />
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(position)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    编辑
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(position.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    删除
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 员工列表表格组件
function StaffTable({ staff, positions, onEdit, onDelete }: {
  staff: Staff[]
  positions: Position[]
  onEdit: (staff: Staff) => void
  onDelete: (id: string) => void
}) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getPositionName = (positionId?: string) => {
    if (!positionId) return '-'
    const position = positions.find(p => p.id === positionId)
    return position ? position.name : '-'
  }

  const getStatusColor = (status: Staff['status']) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    }
    return colors[status]
  }

  const getStatusLabel = (status: Staff['status']) => {
    const labels = {
      active: '在职',
      inactive: '离职',
      pending: '待入职'
    }
    return labels[status]
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              员工名称
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              账号
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              岗位
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              创建时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {staff.map((member) => (
            <tr key={member.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-semibold text-sm">
                      {member.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{member.name}</div>
                    <div className="text-sm text-gray-500">{member.email}</div>
                  </div>
                  <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(member.status)}`}>
                    {getStatusLabel(member.status)}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{member.account}</div>
                <div className="text-sm text-gray-500">{member.phone}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{getPositionName(member.positionId)}</div>
                <div className="text-sm text-gray-500">
                  {member.department === 'administration' ? '行政部' :
                   member.department === 'education' ? '教务部' :
                   member.department === 'finance' ? '财务部' :
                   member.department === 'marketing' ? '市场部' :
                   member.department === 'hr' ? '人事部' :
                   member.department === 'it' ? 'IT部' : member.department}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {formatDate(member.createdAt)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(member)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    编辑
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(member.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    删除
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function PositionForm({ 
  position, 
  onSave, 
  onCancel 
}: { 
  position?: Position | null
  onSave: (position: PositionFormData) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState<PositionFormData>({
    name: position?.name || '',
    description: position?.description || '',
    department: position?.department || 'administration',
    level: position?.level || 'junior',
    permissions: position?.permissions || [],
    requirements: position?.requirements || [],
    isActive: position?.isActive ?? true
  })

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    if (formData.name && formData.description) {
      onSave(formData)
    }
  }

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        permissions: [...formData.permissions, permission]
      })
    } else {
      setFormData({
        ...formData,
        permissions: formData.permissions.filter(p => p !== permission)
      })
    }
  }

  const availablePermissions = [
    { id: 'user_management', label: '用户管理' },
    { id: 'course_management', label: '课程管理' },
    { id: 'student_management', label: '学员管理' },
    { id: 'finance_management', label: '财务管理' },
    { id: 'data_analysis', label: '数据分析' },
    { id: 'system_settings', label: '系统设置' },
    { id: 'staff_management', label: '员工管理' },
    { id: 'teacher_management', label: '教师管理' },
    { id: 'institution_management', label: '机构管理' }
  ]

  const departments = [
    { value: 'administration', label: '行政部' },
    { value: 'education', label: '教务部' },
    { value: 'finance', label: '财务部' },
    { value: 'marketing', label: '市场部' },
    { value: 'hr', label: '人事部' },
    { value: 'it', label: 'IT部' }
  ]

  const levels = [
    { value: 'junior', label: '初级' },
    { value: 'middle', label: '中级' },
    { value: 'senior', label: '高级' },
    { value: 'manager', label: '经理' },
    { value: 'director', label: '总监' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {position ? '编辑岗位' : '创建岗位'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">岗位名称 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                <select
                  value={formData.department}
                  onChange={(e) => setFormData({
                    ...formData,
                    department: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {departments.map(dept => (
                    <option key={dept.value} value={dept.value}>{dept.label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">岗位级别</label>
                <select
                  value={formData.level}
                  onChange={(e) => setFormData({
                    ...formData,
                    level: (e.target as HTMLSelectElement).value as Position['level']
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {levels.map(level => (
                    <option key={level.value} value={level.value}>{level.label}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({
                      ...formData,
                      isActive: (e.target as HTMLInputElement).checked
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">启用此岗位</span>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">岗位描述 *</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({
                  ...formData,
                  description: (e.target as HTMLTextAreaElement).value
                })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入岗位职责和工作内容..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">岗位权限</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {availablePermissions.map((permission) => (
                  <label key={permission.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission.id)}
                      onChange={(e) => handlePermissionChange(permission.id, (e.target as HTMLInputElement).checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{permission.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">任职要求</label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setFormData({
                    ...formData,
                    requirements: [...formData.requirements, '']
                  })}
                >
                  + 添加要求
                </Button>
              </div>
              <div className="space-y-2">
                {formData.requirements.map((requirement, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={requirement}
                      onChange={(e) => {
                        const newRequirements = [...formData.requirements]
                        newRequirements[index] = (e.target as HTMLInputElement).value
                        setFormData({
                          ...formData,
                          requirements: newRequirements
                        })
                      }}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入任职要求..."
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setFormData({
                        ...formData,
                        requirements: formData.requirements.filter((_, i) => i !== index)
                      })}
                      className="text-red-600 hover:text-red-700"
                    >
                      删除
                    </Button>
                  </div>
                ))}
                {formData.requirements.length === 0 && (
                  <p className="text-sm text-gray-500">暂无任职要求，点击上方按钮添加</p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                取消
              </Button>
              <Button type="submit">
                {position ? '更新' : '创建'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export function StaffManagement() {
  const [activeTab, setActiveTab] = useState<'staff' | 'positions'>('staff')
  
  const [positions, setPositions] = useState<Position[]>([
    {
      id: '1',
      name: '教务主管',
      description: '负责教务管理工作，包括课程安排、教师管理、学员管理等',
      department: 'education',
      level: 'manager',
      permissions: ['course_management', 'student_management', 'teacher_management'],
      requirements: ['教育相关专业本科以上学历', '3年以上教务管理经验', '良好的沟通协调能力'],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '财务专员',
      description: '负责日常财务工作，包括账务处理、报表制作、费用管理等',
      department: 'finance',
      level: 'middle',
      permissions: ['finance_management'],
      requirements: ['财务相关专业', '熟练使用财务软件', '具备会计从业资格证'],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '3',
      name: '市场经理',
      description: '负责市场推广和客户开发工作，制定营销策略',
      department: 'marketing',
      level: 'manager',
      permissions: ['data_analysis'],
      requirements: ['市场营销相关专业', '2年以上市场工作经验', '具备良好的客户沟通能力', '熟悉数字营销工具'],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '4',
      name: 'IT专员',
      description: '负责系统维护、技术支持和软件开发工作',
      department: 'it',
      level: 'middle',
      permissions: ['system_settings'],
      requirements: ['计算机相关专业', '熟悉常用编程语言', '具备系统运维经验'],
      isActive: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ])
  
  const [staff, setStaff] = useState<Staff[]>([
    {
      id: '1',
      name: '张三',
      account: 'zhangsan',
      email: '<EMAIL>',
      phone: '***********',
      position: '教务主管',
      positionId: '1',
      department: 'education',
      status: 'active',
      joinDate: '2023-01-15',
      createdAt: '2023-01-15T08:00:00Z',
      permissions: ['course_management', 'student_management'],
      salary: 8000
    },
    {
      id: '2',
      name: '李四',
      account: 'lisi',
      email: '<EMAIL>',
      phone: '***********',
      position: '财务专员',
      positionId: '2',
      department: 'finance',
      status: 'active',
      joinDate: '2023-03-20',
      createdAt: '2023-03-20T09:00:00Z',
      permissions: ['finance_management'],
      salary: 7000
    },
    {
      id: '3',
      name: '王五',
      account: 'wangwu',
      email: '<EMAIL>',
      phone: '***********',
      position: '市场经理',
      positionId: '3',
      department: 'marketing',
      status: 'pending',
      joinDate: '2024-01-10',
      createdAt: '2024-01-10T10:00:00Z',
      permissions: ['data_analysis'],
      salary: 9000
    }
  ])
  
  // 岗位管理状态
  const [showPositionForm, setShowPositionForm] = useState(false)
  const [editingPosition, setEditingPosition] = useState<Position | null>(null)
  const [positionSearchTerm, setPositionSearchTerm] = useState('')
  const [filterPositionDepartment, setFilterPositionDepartment] = useState('all')
  const [filterPositionStatus, setFilterPositionStatus] = useState('all')

  // 员工管理状态
  const [showStaffForm, setShowStaffForm] = useState(false)
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null)
  const [staffSearchTerm, setStaffSearchTerm] = useState('')
  const [filterStaffDepartment, setFilterStaffDepartment] = useState('all')
  const [filterStaffStatus, setFilterStaffStatus] = useState('all')

  const handleAddPosition = () => {
    setEditingPosition(null)
    setShowPositionForm(true)
  }

  const handleEditPosition = (position: Position) => {
    setEditingPosition(position)
    setShowPositionForm(true)
  }

  const handleSavePosition = (positionData: PositionFormData) => {
    if (editingPosition) {
      setPositions(positions.map(p =>
        p.id === editingPosition.id
          ? {
              ...positionData,
              id: editingPosition.id,
              createdAt: editingPosition.createdAt,
              updatedAt: new Date().toISOString()
            }
          : p
      ))
    } else {
      const newPosition: Position = {
        ...positionData,
        id: (positions.length + 1).toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      setPositions([...positions, newPosition])
    }
    setShowPositionForm(false)
    setEditingPosition(null)
  }

  const handleDeletePosition = (id: string) => {
    const staffUsingPosition = staff.filter(s => s.positionId === id)
    if (staffUsingPosition.length > 0) {
      alert(`无法删除此岗位，还有 ${staffUsingPosition.length} 名员工正在使用此岗位`)
      return
    }

    if (confirm('确定要删除这个岗位吗？')) {
      setPositions(positions.filter(p => p.id !== id))
    }
  }

  // 员工管理函数
  const handleAddStaff = () => {
    setEditingStaff(null)
    setShowStaffForm(true)
  }

  const handleEditStaff = (staff: Staff) => {
    setEditingStaff(staff)
    setShowStaffForm(true)
  }

  const handleDeleteStaff = (id: string) => {
    if (confirm('确定要删除这个员工吗？')) {
      setStaff(staff.filter(s => s.id !== id))
    }
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">员工管理</h1>
          <p className="text-gray-600 mt-1">管理机构的员工信息和岗位设置</p>
        </div>

        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'staff'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              员工列表
            </button>
            <button
              onClick={() => setActiveTab('positions')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'positions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              岗位管理
            </button>
          </nav>
        </div>

        {activeTab === 'positions' && (
          <div>
            <div className="flex justify-end mb-6">
              <Button onClick={handleAddPosition}>
                + 创建岗位
              </Button>
            </div>

            {/* 岗位搜索和筛选 */}
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="搜索岗位名称或描述..."
                      value={positionSearchTerm}
                      onChange={(e) => setPositionSearchTerm((e.target as HTMLInputElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="sm:w-48">
                    <select
                      value={filterPositionDepartment}
                      onChange={(e) => setFilterPositionDepartment((e.target as HTMLSelectElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">所有部门</option>
                      <option value="administration">行政部</option>
                      <option value="education">教务部</option>
                      <option value="finance">财务部</option>
                      <option value="marketing">市场部</option>
                      <option value="hr">人事部</option>
                      <option value="it">IT部</option>
                    </select>
                  </div>
                  <div className="sm:w-32">
                    <select
                      value={filterPositionStatus}
                      onChange={(e) => setFilterPositionStatus((e.target as HTMLSelectElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">所有状态</option>
                      <option value="active">启用</option>
                      <option value="inactive">禁用</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {(() => {
              const filteredPositions = positions.filter(position => {
                const matchesSearch = position.name.toLowerCase().includes(positionSearchTerm.toLowerCase()) ||
                                     position.description.toLowerCase().includes(positionSearchTerm.toLowerCase())

                const matchesDepartment = filterPositionDepartment === 'all' || position.department === filterPositionDepartment
                const matchesStatus = filterPositionStatus === 'all' ||
                                     (filterPositionStatus === 'active' && position.isActive) ||
                                     (filterPositionStatus === 'inactive' && !position.isActive)

                return matchesSearch && matchesDepartment && matchesStatus
              })

              return filteredPositions.length > 0 ? (
                <PositionTable
                  positions={filteredPositions}
                  onEdit={handleEditPosition}
                  onDelete={handleDeletePosition}
                />
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无岗位信息</h3>
                    <p className="text-gray-500 mb-4">您还没有创建任何岗位，点击上方按钮创建第一个岗位</p>
                    <Button onClick={handleAddPosition}>
                      创建岗位
                    </Button>
                  </CardContent>
                </Card>
              )
            })()}
          </div>
        )}

        {activeTab === 'staff' && (
          <div>
            <div className="flex justify-end mb-6">
              <Button onClick={handleAddStaff}>
                + 添加员工
              </Button>
            </div>

            {/* 员工搜索和筛选 */}
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="搜索员工姓名、账号或邮箱..."
                      value={staffSearchTerm}
                      onChange={(e) => setStaffSearchTerm((e.target as HTMLInputElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="sm:w-48">
                    <select
                      value={filterStaffDepartment}
                      onChange={(e) => setFilterStaffDepartment((e.target as HTMLSelectElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">所有部门</option>
                      <option value="administration">行政部</option>
                      <option value="education">教务部</option>
                      <option value="finance">财务部</option>
                      <option value="marketing">市场部</option>
                      <option value="hr">人事部</option>
                      <option value="it">IT部</option>
                    </select>
                  </div>
                  <div className="sm:w-32">
                    <select
                      value={filterStaffStatus}
                      onChange={(e) => setFilterStaffStatus((e.target as HTMLSelectElement).value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">所有状态</option>
                      <option value="active">在职</option>
                      <option value="inactive">离职</option>
                      <option value="pending">待入职</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 员工列表 */}
            {(() => {
              const filteredStaff = staff.filter(member => {
                const matchesSearch = member.name.toLowerCase().includes(staffSearchTerm.toLowerCase()) ||
                                     member.account.toLowerCase().includes(staffSearchTerm.toLowerCase()) ||
                                     member.email.toLowerCase().includes(staffSearchTerm.toLowerCase())

                const matchesDepartment = filterStaffDepartment === 'all' || member.department === filterStaffDepartment
                const matchesStatus = filterStaffStatus === 'all' || member.status === filterStaffStatus

                return matchesSearch && matchesDepartment && matchesStatus
              })

              return filteredStaff.length > 0 ? (
                <StaffTable
                  staff={filteredStaff}
                  positions={positions}
                  onEdit={handleEditStaff}
                  onDelete={handleDeleteStaff}
                />
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无员工信息</h3>
                    <p className="text-gray-500 mb-4">您还没有添加任何员工，点击上方按钮添加第一个员工</p>
                    <Button onClick={handleAddStaff}>
                      添加员工
                    </Button>
                  </CardContent>
                </Card>
              )
            })()}
          </div>
        )}

        {showPositionForm && (
          <PositionForm
            position={editingPosition}
            onSave={handleSavePosition}
            onCancel={() => {
              setShowPositionForm(false)
              setEditingPosition(null)
            }}
          />
        )}
      </div>
    </div>
  )
}
