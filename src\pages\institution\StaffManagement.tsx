import { useState } from 'preact/hooks'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Position {
  id: string
  name: string
  description: string
  department: string
  level: 'junior' | 'middle' | 'senior' | 'manager' | 'director'
  permissions: string[]
  requirements: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface Staff {
  id: string
  name: string
  email: string
  phone: string
  position: string
  positionId?: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  avatar?: string
  permissions: string[]
  salary?: number
  address?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
}

interface StaffFormData extends Omit<Staff, 'id'> {}
interface PositionFormData extends Omit<Position, 'id' | 'createdAt' | 'updatedAt'> {}

function PositionForm({ 
  position, 
  onSave, 
  onCancel 
}: { 
  position?: Position | null
  onSave: (position: PositionFormData) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState<PositionFormData>({
    name: position?.name || '',
    description: position?.description || '',
    department: position?.department || 'administration',
    level: position?.level || 'junior',
    permissions: position?.permissions || [],
    requirements: position?.requirements || [],
    isActive: position?.isActive ?? true
  })

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    if (formData.name && formData.description) {
      onSave(formData)
    }
  }

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        permissions: [...formData.permissions, permission]
      })
    } else {
      setFormData({
        ...formData,
        permissions: formData.permissions.filter(p => p !== permission)
      })
    }
  }

  const availablePermissions = [
    { id: 'user_management', label: '用户管理' },
    { id: 'course_management', label: '课程管理' },
    { id: 'student_management', label: '学员管理' },
    { id: 'finance_management', label: '财务管理' },
    { id: 'data_analysis', label: '数据分析' },
    { id: 'system_settings', label: '系统设置' },
    { id: 'staff_management', label: '员工管理' },
    { id: 'teacher_management', label: '教师管理' },
    { id: 'institution_management', label: '机构管理' }
  ]

  const departments = [
    { value: 'administration', label: '行政部' },
    { value: 'education', label: '教务部' },
    { value: 'finance', label: '财务部' },
    { value: 'marketing', label: '市场部' },
    { value: 'hr', label: '人事部' },
    { value: 'it', label: 'IT部' }
  ]

  const levels = [
    { value: 'junior', label: '初级' },
    { value: 'middle', label: '中级' },
    { value: 'senior', label: '高级' },
    { value: 'manager', label: '经理' },
    { value: 'director', label: '总监' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {position ? '编辑岗位' : '创建岗位'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">岗位名称 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                <select
                  value={formData.department}
                  onChange={(e) => setFormData({
                    ...formData,
                    department: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {departments.map(dept => (
                    <option key={dept.value} value={dept.value}>{dept.label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">岗位级别</label>
                <select
                  value={formData.level}
                  onChange={(e) => setFormData({
                    ...formData,
                    level: (e.target as HTMLSelectElement).value as Position['level']
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {levels.map(level => (
                    <option key={level.value} value={level.value}>{level.label}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({
                      ...formData,
                      isActive: (e.target as HTMLInputElement).checked
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">启用此岗位</span>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">岗位描述 *</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({
                  ...formData,
                  description: (e.target as HTMLTextAreaElement).value
                })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入岗位职责和工作内容..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">岗位权限</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {availablePermissions.map((permission) => (
                  <label key={permission.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission.id)}
                      onChange={(e) => handlePermissionChange(permission.id, (e.target as HTMLInputElement).checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{permission.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                取消
              </Button>
              <Button type="submit">
                {position ? '更新' : '创建'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export function StaffManagement() {
  const [activeTab, setActiveTab] = useState<'staff' | 'positions'>('staff')
  
  const [positions, setPositions] = useState<Position[]>([
    {
      id: '1',
      name: '教务主管',
      description: '负责教务管理工作，包括课程安排、教师管理、学员管理等',
      department: 'education',
      level: 'manager',
      permissions: ['course_management', 'student_management', 'teacher_management'],
      requirements: ['教育相关专业本科以上学历', '3年以上教务管理经验'],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ])
  
  const [staff, setStaff] = useState<Staff[]>([
    {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138001',
      position: '教务主管',
      positionId: '1',
      department: 'education',
      status: 'active',
      joinDate: '2023-01-15',
      permissions: ['course_management', 'student_management'],
      salary: 8000
    }
  ])
  
  const [showPositionForm, setShowPositionForm] = useState(false)
  const [editingPosition, setEditingPosition] = useState<Position | null>(null)

  const handleAddPosition = () => {
    setEditingPosition(null)
    setShowPositionForm(true)
  }

  const handleEditPosition = (position: Position) => {
    setEditingPosition(position)
    setShowPositionForm(true)
  }

  const handleSavePosition = (positionData: PositionFormData) => {
    if (editingPosition) {
      setPositions(positions.map(p =>
        p.id === editingPosition.id
          ? {
              ...positionData,
              id: editingPosition.id,
              createdAt: editingPosition.createdAt,
              updatedAt: new Date().toISOString()
            }
          : p
      ))
    } else {
      const newPosition: Position = {
        ...positionData,
        id: (positions.length + 1).toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      setPositions([...positions, newPosition])
    }
    setShowPositionForm(false)
    setEditingPosition(null)
  }

  const handleDeletePosition = (id: string) => {
    const staffUsingPosition = staff.filter(s => s.positionId === id)
    if (staffUsingPosition.length > 0) {
      alert(`无法删除此岗位，还有 ${staffUsingPosition.length} 名员工正在使用此岗位`)
      return
    }

    if (confirm('确定要删除这个岗位吗？')) {
      setPositions(positions.filter(p => p.id !== id))
    }
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">员工管理</h1>
          <p className="text-gray-600 mt-1">管理机构的员工信息和岗位设置</p>
        </div>

        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'staff'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              员工列表
            </button>
            <button
              onClick={() => setActiveTab('positions')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'positions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              岗位管理
            </button>
          </nav>
        </div>

        {activeTab === 'positions' && (
          <div>
            <div className="flex justify-end mb-6">
              <Button onClick={handleAddPosition}>
                + 创建岗位
              </Button>
            </div>

            <div className="grid gap-4">
              {positions.map((position) => (
                <Card key={position.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{position.name}</h3>
                        <p className="text-sm text-gray-600 mb-3">{position.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{position.department === 'education' ? '教务部' : position.department}</span>
                          <span>{position.level === 'manager' ? '经理' : position.level}</span>
                          <span className={position.isActive ? 'text-green-600' : 'text-red-600'}>
                            {position.isActive ? '启用' : '禁用'}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingPosition(position)
                            setShowPositionForm(true)
                          }}
                        >
                          编辑
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'staff' && (
          <div>
            <p className="text-gray-500">员工列表功能开发中...</p>
          </div>
        )}

        {showPositionForm && (
          <PositionForm
            position={editingPosition}
            onSave={handleSavePosition}
            onCancel={() => {
              setShowPositionForm(false)
              setEditingPosition(null)
            }}
          />
        )}
      </div>
    </div>
  )
}
