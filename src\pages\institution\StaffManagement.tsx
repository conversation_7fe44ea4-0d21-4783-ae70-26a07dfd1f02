import { useState } from 'preact/hooks'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Staff {
  id: string
  name: string
  email: string
  phone: string
  position: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  avatar?: string
  permissions: string[]
  salary?: number
  address?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
}

interface StaffFormData extends Omit<Staff, 'id'> {}

function StaffForm({ 
  staff, 
  onSave, 
  onCancel 
}: { 
  staff?: Staff | null
  onSave: (staff: StaffFormData) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState<StaffFormData>({
    name: staff?.name || '',
    email: staff?.email || '',
    phone: staff?.phone || '',
    position: staff?.position || '',
    department: staff?.department || 'administration',
    status: staff?.status || 'active',
    joinDate: staff?.joinDate || new Date().toISOString().split('T')[0],
    permissions: staff?.permissions || [],
    salary: staff?.salary || 0,
    address: staff?.address || '',
    emergencyContact: staff?.emergencyContact || {
      name: '',
      phone: '',
      relationship: ''
    }
  })

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    if (formData.name && formData.email && formData.phone && formData.position) {
      onSave(formData)
    }
  }

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        permissions: [...formData.permissions, permission]
      })
    } else {
      setFormData({
        ...formData,
        permissions: formData.permissions.filter(p => p !== permission)
      })
    }
  }

  const availablePermissions = [
    { id: 'user_management', label: '用户管理' },
    { id: 'course_management', label: '课程管理' },
    { id: 'student_management', label: '学员管理' },
    { id: 'finance_management', label: '财务管理' },
    { id: 'data_analysis', label: '数据分析' },
    { id: 'system_settings', label: '系统设置' }
  ]

  const departments = [
    { value: 'administration', label: '行政部' },
    { value: 'education', label: '教务部' },
    { value: 'finance', label: '财务部' },
    { value: 'marketing', label: '市场部' },
    { value: 'hr', label: '人事部' },
    { value: 'it', label: 'IT部' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {staff ? '编辑员工' : '添加员工'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">姓名 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">邮箱 *</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({
                    ...formData,
                    email: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">手机号 *</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({
                    ...formData,
                    phone: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">职位 *</label>
                <input
                  type="text"
                  value={formData.position}
                  onChange={(e) => setFormData({
                    ...formData,
                    position: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">部门</label>
                <select
                  value={formData.department}
                  onChange={(e) => setFormData({
                    ...formData,
                    department: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {departments.map(dept => (
                    <option key={dept.value} value={dept.value}>{dept.label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({
                    ...formData,
                    status: (e.target as HTMLSelectElement).value as Staff['status']
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">在职</option>
                  <option value="inactive">离职</option>
                  <option value="pending">待入职</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">入职日期</label>
                <input
                  type="date"
                  value={formData.joinDate}
                  onChange={(e) => setFormData({
                    ...formData,
                    joinDate: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">薪资</label>
                <input
                  type="number"
                  value={formData.salary}
                  onChange={(e) => setFormData({
                    ...formData,
                    salary: parseInt((e.target as HTMLInputElement).value) || 0
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="月薪（元）"
                />
              </div>
            </div>

            {/* 地址 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">地址</label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData({
                  ...formData,
                  address: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="详细地址"
              />
            </div>

            {/* 紧急联系人 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">紧急联系人</label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <input
                    type="text"
                    value={formData.emergencyContact?.name || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      emergencyContact: {
                        ...formData.emergencyContact!,
                        name: (e.target as HTMLInputElement).value
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="姓名"
                  />
                </div>
                <div>
                  <input
                    type="tel"
                    value={formData.emergencyContact?.phone || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      emergencyContact: {
                        ...formData.emergencyContact!,
                        phone: (e.target as HTMLInputElement).value
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="电话"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    value={formData.emergencyContact?.relationship || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      emergencyContact: {
                        ...formData.emergencyContact!,
                        relationship: (e.target as HTMLInputElement).value
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="关系"
                  />
                </div>
              </div>
            </div>

            {/* 权限设置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">权限设置</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {availablePermissions.map((permission) => (
                  <label key={permission.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission.id)}
                      onChange={(e) => handlePermissionChange(permission.id, (e.target as HTMLInputElement).checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{permission.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                取消
              </Button>
              <Button type="submit">
                {staff ? '更新' : '添加'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

function StaffList({ staff, onEdit, onDelete }: {
  staff: Staff[]
  onEdit: (staff: Staff) => void
  onDelete: (id: string) => void
}) {
  const getDepartmentLabel = (department: string) => {
    const departments = {
      administration: '行政部',
      education: '教务部',
      finance: '财务部',
      marketing: '市场部',
      hr: '人事部',
      it: 'IT部'
    }
    return departments[department as keyof typeof departments] || department
  }

  const getStatusColor = (status: Staff['status']) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    }
    return colors[status]
  }

  const getStatusLabel = (status: Staff['status']) => {
    const labels = {
      active: '在职',
      inactive: '离职',
      pending: '待入职'
    }
    return labels[status]
  }

  return (
    <div className="grid gap-4">
      {staff.map((member) => (
        <Card key={member.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-lg">
                    {member.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{member.name}</h3>
                  <p className="text-sm text-gray-600">{member.position}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm text-gray-500">{getDepartmentLabel(member.department)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(member.status)}`}>
                      {getStatusLabel(member.status)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(member)}
                >
                  编辑
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(member.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  删除
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">邮箱</p>
                <p className="text-gray-900">{member.email}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">手机号</p>
                <p className="text-gray-900">{member.phone}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">入职日期</p>
                <p className="text-gray-900">{member.joinDate}</p>
              </div>
            </div>

            {member.permissions.length > 0 && (
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-2">权限</p>
                <div className="flex flex-wrap gap-2">
                  {member.permissions.map((permission, index) => (
                    <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">
                      {permission}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function StaffManagement() {
  const [staff, setStaff] = useState<Staff[]>([
    {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138001',
      position: '教务主管',
      department: 'education',
      status: 'active',
      joinDate: '2023-01-15',
      permissions: ['course_management', 'student_management'],
      salary: 8000,
      address: '上海市浦东新区张江高科技园区'
    },
    {
      id: '2',
      name: '李四',
      email: '<EMAIL>',
      phone: '13800138002',
      position: '财务专员',
      department: 'finance',
      status: 'active',
      joinDate: '2023-03-20',
      permissions: ['finance_management'],
      salary: 7000,
      address: '上海市静安区南京西路'
    },
    {
      id: '3',
      name: '王五',
      email: '<EMAIL>',
      phone: '13800138003',
      position: '市场经理',
      department: 'marketing',
      status: 'pending',
      joinDate: '2024-01-10',
      permissions: ['data_analysis'],
      salary: 9000
    }
  ])

  const [showForm, setShowForm] = useState(false)
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterDepartment, setFilterDepartment] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')

  const handleAddStaff = () => {
    setEditingStaff(null)
    setShowForm(true)
  }

  const handleEditStaff = (staff: Staff) => {
    setEditingStaff(staff)
    setShowForm(true)
  }

  const handleSaveStaff = (staffData: StaffFormData) => {
    if (editingStaff) {
      setStaff(staff.map(s =>
        s.id === editingStaff.id
          ? { ...staffData, id: editingStaff.id }
          : s
      ))
    } else {
      const newStaff: Staff = {
        ...staffData,
        id: (staff.length + 1).toString()
      }
      setStaff([...staff, newStaff])
    }
    setShowForm(false)
    setEditingStaff(null)
  }

  const handleDeleteStaff = (id: string) => {
    if (confirm('确定要删除这个员工吗？')) {
      setStaff(staff.filter(s => s.id !== id))
    }
  }

  const filteredStaff = staff.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.position.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesDepartment = filterDepartment === 'all' || member.department === filterDepartment
    const matchesStatus = filterStatus === 'all' || member.status === filterStatus

    return matchesSearch && matchesDepartment && matchesStatus
  })

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">员工管理</h1>
            <p className="text-gray-600 mt-1">管理机构的所有员工信息</p>
          </div>
          <Button onClick={handleAddStaff}>
            + 添加员工
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索员工姓名、邮箱或职位..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm((e.target as HTMLInputElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="sm:w-48">
                <select
                  value={filterDepartment}
                  onChange={(e) => setFilterDepartment((e.target as HTMLSelectElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有部门</option>
                  <option value="administration">行政部</option>
                  <option value="education">教务部</option>
                  <option value="finance">财务部</option>
                  <option value="marketing">市场部</option>
                  <option value="hr">人事部</option>
                  <option value="it">IT部</option>
                </select>
              </div>
              <div className="sm:w-32">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus((e.target as HTMLSelectElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有状态</option>
                  <option value="active">在职</option>
                  <option value="inactive">离职</option>
                  <option value="pending">待入职</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 员工列表 */}
        {filteredStaff.length > 0 ? (
          <StaffList
            staff={filteredStaff}
            onEdit={handleEditStaff}
            onDelete={handleDeleteStaff}
          />
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无员工信息</h3>
              <p className="text-gray-500 mb-4">您还没有添加任何员工，点击上方按钮添加第一个员工</p>
              <Button onClick={handleAddStaff}>
                添加员工
              </Button>
            </CardContent>
          </Card>
        )}

        {/* 员工表单弹窗 */}
        {showForm && (
          <StaffForm
            staff={editingStaff}
            onSave={handleSaveStaff}
            onCancel={() => {
              setShowForm(false)
              setEditingStaff(null)
            }}
          />
        )}
      </div>
    </div>
  )
}
